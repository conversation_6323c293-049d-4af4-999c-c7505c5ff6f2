<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simplified Admin Dashboard - Skills Assess</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="user-menu.css">
    
    <style>
        /* Custom styles for simplified dashboard */
        .simplified-dashboard {
            font-family: 'Montserrat', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .dashboard-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e3a8a;
            margin-bottom: 0.5rem;
        }
        
        .dashboard-subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }
        
        .students-table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .table-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e3a8a;
        }
        
        .students-count {
            background: #1e3a8a;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .students-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .students-table th {
            background: #f8fafc;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .students-table td {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            vertical-align: middle;
        }
        
        .students-table tr:hover {
            background: #f8fafc;
            transition: background-color 0.2s ease;
        }
        
        .assessment-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }
        
        .assessment-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .badge-completed {
            background: #dcfce7;
            color: #166534;
            border-color: #bbf7d0;
        }
        
        .badge-not-completed {
            background: #fef3c7;
            color: #92400e;
            border-color: #fde68a;
        }
        
        .badge-pass {
            background: #dcfce7;
            color: #166534;
            border-color: #bbf7d0;
        }
        
        .badge-fail {
            background: #fee2e2;
            color: #dc2626;
            border-color: #fecaca;
        }
        
        .skeleton-loader {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
            height: 1rem;
        }
        
        .skeleton-loader.wide {
            width: 100%;
        }
        
        .skeleton-loader.medium {
            width: 60%;
        }
        
        .skeleton-loader.narrow {
            width: 40%;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1e3a8a;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive design */
        @media (max-width: 1024px) {
            .dashboard-container {
                padding: 1rem;
            }
            
            .dashboard-title {
                font-size: 2rem;
            }
            
            .students-table-container {
                overflow-x: auto;
            }
            
            .students-table {
                min-width: 800px;
            }
        }
        
        @media (max-width: 640px) {
            .dashboard-header {
                padding: 1.5rem;
            }
            
            .dashboard-title {
                font-size: 1.75rem;
            }
            
            .table-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body class="simplified-dashboard">
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    <!-- Top Navigation Bar -->
    <header class="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo and Title -->
                <div class="flex items-center">
                    <img src="logo.png" alt="Skills Assess Logo" class="h-8 w-auto mr-3">
                    <h1 class="text-xl font-semibold text-gray-900">Skills Assess Dashboard</h1>
                </div>

                <!-- User Menu -->
                <div class="relative">
                    <button id="user-menu-button" class="rounded-full p-2" title="Toggle User Menu">
                        <img id="user-menu-avatar" alt="Avatar" class="rounded-full" src="profile.png" width="32" height="32">
                    </button>

                    <!-- User Menu Backdrop -->
                    <div id="user-menu-backdrop" class="hidden"></div>

                    <!-- User Menu Sidebar -->
                    <div id="user-menu" class="hidden">
                        <!-- Close Button -->
                        <div id="user-menu-close">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </div>

                        <!-- User Info Section -->
                        <div class="user-info-section">
                            <div class="user-avatar">
                                <img id="user-menu-profile-pic" src="profile.png" alt="Profile Picture">
                            </div>
                            <div class="user-details">
                                <div id="user-menu-name" class="user-name">Loading...</div>
                                <div id="user-menu-email" class="user-email">Loading...</div>
                                <div id="user-menu-company" class="user-company">Loading...</div>
                            </div>
                        </div>

                        <!-- Edit Profile Option -->
                        <a href="#" id="edit-profile" class="menu-item">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Profile
                        </a>

                        <!-- Credits Section -->
                        <div class="credits-section">
                            <div class="credits-header">
                                <img src="credits.png" alt="Credits" class="w-4 h-4">
                                <span>Credits</span>
                            </div>
                            <div class="credits-balance">
                                <span class="font-semibold">0</span>
                            </div>
                        </div>

                        <!-- Logout Option -->
                        <a id="user-logout" href="#" class="menu-item border-t border-gray-200">
                            <img src="logout.png" alt="Logout" class="w-3 h-3 mr-1">
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-20">
        <div class="dashboard-container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <h1 class="dashboard-title">Student Assessment Overview</h1>
                <p class="dashboard-subtitle">Monitor and review all student assessment progress and results</p>
            </div>

            <!-- Students Table -->
            <div class="students-table-container">
                <div class="table-header">
                    <h2 class="table-title">All Students</h2>
                    <div class="students-count" id="students-count">Loading...</div>
                </div>

                <div class="overflow-x-auto">
                    <table class="students-table">
                        <thead>
                            <tr>
                                <th>Student Name</th>
                                <th>Email</th>
                                <th>English Assessment</th>
                                <th>Mathematics Assessment</th>
                                <th>Last Activity</th>
                            </tr>
                        </thead>
                        <tbody id="students-table-body">
                            <!-- Loading skeleton rows -->
                            <tr>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader narrow"></div></td>
                            </tr>
                            <tr>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader narrow"></div></td>
                            </tr>
                            <tr>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader narrow"></div></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="skills-gap-modal.js"></script>
    <script src="english-results-modal.js"></script>
    <script src="english-assessment-review-modal.js"></script>
    <script src="math-results-modal.js"></script>
    <script src="math-assessment-review-modal.js"></script>
    <script src="user-menu.js"></script>
    <script src="accountmanagement.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "************",
            appId: "1:************:web:223af318437eb5d947d5c9"
        };

        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }
        const db = firebase.firestore();

        // Global variables
        let currentUser = null;
        let userCompany = null;
        let studentsData = [];

        // Utility functions
        function showLoadingOverlay() {
            document.getElementById('loading-overlay').style.display = 'flex';
        }

        function hideLoadingOverlay() {
            document.getElementById('loading-overlay').style.display = 'none';
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'Never';

            let date;
            if (timestamp.toDate) {
                date = timestamp.toDate();
            } else if (timestamp instanceof Date) {
                date = timestamp;
            } else {
                date = new Date(timestamp);
            }

            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // English assessment badge generation
        function getEnglishAssessmentBadge(englishData) {
            if (!englishData || !englishData.englishAssessmentCompleted) {
                return {
                    html: '<span class="assessment-badge badge-not-completed">Not Completed</span>',
                    clickable: false
                };
            }

            const score = englishData.englishProficiencyScore || 0;
            const level = englishData.englishProficiencyLevel || 'Entry';
            const isPass = score >= 16;

            const badgeClass = isPass ? 'badge-pass' : 'badge-fail';
            const statusText = `${level} (${score}/21)`;

            return {
                html: `<span class="assessment-badge ${badgeClass} english-badge-clickable"
                             data-user-email="${englishData.userEmail}"
                             data-user-name="${englishData.userName}"
                             title="Click to view detailed English assessment results">
                         ${statusText}
                       </span>`,
                clickable: true
            };
        }

        // Mathematics assessment badge generation
        function getMathAssessmentBadge(mathData) {
            if (!mathData || !mathData.mathAssessmentCompleted) {
                return {
                    html: '<span class="assessment-badge badge-not-completed">Not Completed</span>',
                    clickable: false
                };
            }

            const score = mathData.mathOverallScore || 0;
            const level = mathData.mathHighestLevelCompleted || 'Entry';

            return {
                html: `<span class="assessment-badge badge-completed math-badge-clickable"
                             data-user-email="${mathData.userEmail}"
                             data-user-name="${mathData.userName}"
                             title="Click to view detailed Mathematics assessment results">
                         ${level} (${score} pts)
                       </span>`,
                clickable: true
            };
        }

        // Load students data from Firebase
        async function loadStudentsData() {
            try {
                showLoadingOverlay();

                if (!userCompany) {
                    throw new Error('No company information available');
                }

                const companyRef = db.collection('companies').doc(userCompany);
                const usersSnapshot = await companyRef.collection('users').get();

                studentsData = [];

                for (const doc of usersSnapshot.docs) {
                    const userData = doc.data();
                    const userEmail = doc.id;

                    // Filter for students only
                    if (userData.userType !== 'student') continue;

                    const studentInfo = {
                        email: userEmail,
                        name: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown',
                        firstName: userData.firstName || '',
                        lastName: userData.lastName || '',

                        // English assessment data
                        english: {
                            englishAssessmentCompleted: userData.englishAssessmentCompleted || false,
                            englishProficiencyScore: userData.englishProficiencyScore || 0,
                            englishProficiencyLevel: userData.englishProficiencyLevel || 'Entry',
                            englishAssessmentTimestamp: userData.englishAssessmentTimestamp,
                            userEmail: userEmail,
                            userName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown'
                        },

                        // Mathematics assessment data
                        math: {
                            mathAssessmentCompleted: userData.mathAssessmentCompleted || false,
                            mathOverallScore: userData.mathOverallScore || 0,
                            mathHighestLevelCompleted: userData.mathHighestLevelCompleted || 'Entry',
                            mathAssessmentTimestamp: userData.mathAssessmentTimestamp,
                            userEmail: userEmail,
                            userName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown'
                        },

                        // Activity data
                        lastActivity: userData.lastUpdated || userData.createdAt || null,
                        createdAt: userData.createdAt
                    };

                    studentsData.push(studentInfo);
                }

                // Sort by name
                studentsData.sort((a, b) => a.name.localeCompare(b.name));

                renderStudentsTable();
                updateStudentsCount();

            } catch (error) {
                console.error('Error loading students data:', error);
                showErrorMessage('Failed to load student data. Please refresh the page.');
            } finally {
                hideLoadingOverlay();
            }
        }

        // Render students table
        function renderStudentsTable() {
            const tbody = document.getElementById('students-table-body');

            if (studentsData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-8 text-gray-500">
                            No students found in your organization.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = studentsData.map(student => {
                const englishBadge = getEnglishAssessmentBadge(student.english);
                const mathBadge = getMathAssessmentBadge(student.math);
                const lastActivity = formatDate(student.lastActivity);

                return `
                    <tr>
                        <td class="font-medium text-gray-900">${student.name}</td>
                        <td class="text-gray-600">${student.email}</td>
                        <td>${englishBadge.html}</td>
                        <td>${mathBadge.html}</td>
                        <td class="text-gray-500">${lastActivity}</td>
                    </tr>
                `;
            }).join('');

            // Add click handlers for assessment badges
            addBadgeClickHandlers();
        }

        // Update students count
        function updateStudentsCount() {
            const countElement = document.getElementById('students-count');
            const count = studentsData.length;
            countElement.textContent = `${count} Student${count !== 1 ? 's' : ''}`;
        }

        // Add click handlers for assessment badges
        function addBadgeClickHandlers() {
            // English assessment badge handlers
            document.querySelectorAll('.english-badge-clickable').forEach(badge => {
                badge.addEventListener('click', async function() {
                    const userEmail = this.getAttribute('data-user-email');
                    const userName = this.getAttribute('data-user-name');

                    if (typeof window.EnglishResultsModal !== 'undefined') {
                        try {
                            showLoadingOverlay();
                            await window.EnglishResultsModal.show({}, userEmail, userName, userCompany);
                        } catch (error) {
                            console.error('Error showing English results modal:', error);
                            showErrorMessage('Failed to load English assessment details');
                        } finally {
                            hideLoadingOverlay();
                        }
                    } else {
                        showErrorMessage('English assessment modal not available');
                    }
                });
            });

            // Mathematics assessment badge handlers
            document.querySelectorAll('.math-badge-clickable').forEach(badge => {
                badge.addEventListener('click', async function() {
                    const userEmail = this.getAttribute('data-user-email');
                    const userName = this.getAttribute('data-user-name');

                    if (typeof window.MathResultsModal !== 'undefined') {
                        try {
                            showLoadingOverlay();
                            await window.MathResultsModal.show({}, userEmail, userName, userCompany);
                        } catch (error) {
                            console.error('Error showing Math results modal:', error);
                            showErrorMessage('Failed to load Mathematics assessment details');
                        } finally {
                            hideLoadingOverlay();
                        }
                    } else {
                        showErrorMessage('Mathematics assessment modal not available');
                    }
                });
            });
        }

        // Show error message
        function showErrorMessage(message) {
            // Create a simple toast notification
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }

        // Show success message
        function showSuccessMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // Initialize dashboard
        async function initializeDashboard() {
            try {
                // Wait for Firebase auth to be ready
                await new Promise((resolve) => {
                    firebase.auth().onAuthStateChanged((user) => {
                        if (user) {
                            currentUser = user;
                            resolve();
                        } else {
                            // Redirect to login if not authenticated
                            window.location.href = 'index.html';
                        }
                    });
                });

                // Get admin data to find company
                const adminRef = db.collection('Admins').doc(currentUser.email);
                const adminDoc = await adminRef.get();

                if (adminDoc.exists) {
                    const adminData = adminDoc.data();
                    userCompany = adminData.company;

                    if (userCompany) {
                        await loadStudentsData();
                    } else {
                        showErrorMessage('No company information found for your account');
                    }
                } else {
                    showErrorMessage('Admin account not found');
                }

            } catch (error) {
                console.error('Error initializing dashboard:', error);
                showErrorMessage('Failed to initialize dashboard');
            }
        }

        // Handle authentication state changes
        firebase.auth().onAuthStateChanged((user) => {
            if (user) {
                currentUser = user;
                console.log('User authenticated:', user.email);
            } else {
                console.log('User not authenticated, redirecting to login');
                window.location.href = 'index.html';
            }
        });

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });

        // Refresh data function
        function refreshData() {
            if (userCompany) {
                loadStudentsData();
            }
        }

        // Add keyboard shortcut for refresh (Ctrl+R or F5)
        document.addEventListener('keydown', function(event) {
            if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
                event.preventDefault();
                refreshData();
            }
        });

        // Export functions for global access
        window.SimplifiedDashboard = {
            refreshData,
            loadStudentsData,
            showErrorMessage,
            showSuccessMessage
        };
    </script>
</body>
</html>
